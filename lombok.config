# Configuración de Lombok para gedsys2-bpm-engine

# Configuración general
config.stopBubbling = true

# Configuración de logging
lombok.log.fieldName = log
lombok.log.fieldIsStatic = true

# Configuración de @Builder
lombok.builder.className = Builder

# Configuración de @Accessors
lombok.accessors.chain = false
lombok.accessors.fluent = false

# Configuración de @FieldDefaults
lombok.fieldDefaults.defaultPrivate = true
lombok.fieldDefaults.defaultFinal = false

# Configuración de @ToString
lombok.toString.includeFieldNames = true
lombok.toString.doNotUseGetters = false

# Configuración de @EqualsAndHashCode
lombok.equalsAndHashCode.doNotUseGetters = false
lombok.equalsAndHashCode.callSuper = warn

# Configuración de @Data
lombok.data.flagUsage = ALLOW

# Configuración de @Value
lombok.value.flagUsage = ALLOW

# Configuración de @Getter/@Setter
lombok.getter.flagUsage = ALLOW
lombok.setter.flagUsage = ALLOW

# Configuración de constructores
lombok.noArgsConstructor.flagUsage = ALLOW
lombok.allArgsConstructor.flagUsage = ALLOW
lombok.requiredArgsConstructor.flagUsage = ALLOW

# Configuración de @SneakyThrows
lombok.sneakyThrows.flagUsage = WARNING

# Configuración de @Synchronized
lombok.synchronized.flagUsage = ALLOW

# Configuración de @Cleanup
lombok.cleanup.flagUsage = WARNING

# Configuración específica para Spring Boot
lombok.copyableAnnotations += org.springframework.beans.factory.annotation.Qualifier
lombok.copyableAnnotations += org.springframework.beans.factory.annotation.Value
lombok.copyableAnnotations += org.springframework.boot.context.properties.ConfigurationProperties
lombok.copyableAnnotations += org.springframework.stereotype.Component
lombok.copyableAnnotations += org.springframework.stereotype.Service
lombok.copyableAnnotations += org.springframework.stereotype.Repository
lombok.copyableAnnotations += org.springframework.stereotype.Controller
lombok.copyableAnnotations += org.springframework.web.bind.annotation.RestController

# Configuración para JPA
lombok.copyableAnnotations += jakarta.persistence.Entity
lombok.copyableAnnotations += jakarta.persistence.Table
lombok.copyableAnnotations += jakarta.persistence.Column
lombok.copyableAnnotations += jakarta.persistence.Id
lombok.copyableAnnotations += jakarta.persistence.GeneratedValue
lombok.copyableAnnotations += jakarta.persistence.ManyToOne
lombok.copyableAnnotations += jakarta.persistence.OneToMany
lombok.copyableAnnotations += jakarta.persistence.ManyToMany
lombok.copyableAnnotations += jakarta.persistence.OneToOne

# Configuración para validación
lombok.copyableAnnotations += jakarta.validation.constraints.NotNull
lombok.copyableAnnotations += jakarta.validation.constraints.NotBlank
lombok.copyableAnnotations += jakarta.validation.constraints.NotEmpty
lombok.copyableAnnotations += jakarta.validation.constraints.Size
lombok.copyableAnnotations += jakarta.validation.constraints.Min
lombok.copyableAnnotations += jakarta.validation.constraints.Max
lombok.copyableAnnotations += jakarta.validation.constraints.Pattern
lombok.copyableAnnotations += jakarta.validation.Valid

# Configuración para Jackson
lombok.copyableAnnotations += com.fasterxml.jackson.annotation.JsonProperty
lombok.copyableAnnotations += com.fasterxml.jackson.annotation.JsonIgnore
lombok.copyableAnnotations += com.fasterxml.jackson.annotation.JsonInclude
lombok.copyableAnnotations += com.fasterxml.jackson.annotation.JsonFormat
