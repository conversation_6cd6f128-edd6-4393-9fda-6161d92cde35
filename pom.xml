<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.10</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>co.com.gedsys</groupId>
    <artifactId>gedsys2-bpm-engine</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>gedsys2-bpm-engine</name>
    <description>gedsys2-bpm-engine</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>21</java.version>
        <cibseven-bpm.version>1.1.0</cibseven-bpm.version>
        <skipITs>false</skipITs>
        <org.projectlombok.version>1.18.36</org.projectlombok.version>

    </properties>

    <dependencies>
        <!-- Spring Boot Core -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Databases -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.3</version>
            <scope>runtime</scope>
        </dependency>

        <!-- CIB Seven BPM -->
        <dependency>
            <groupId>org.cibseven.bpm.springboot</groupId>
            <artifactId>cibseven-bpm-spring-boot-starter</artifactId>
            <version>${cibseven-bpm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.cibseven.bpm.springboot</groupId>
            <artifactId>cibseven-bpm-spring-boot-starter-rest</artifactId>
            <version>${cibseven-bpm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.cibseven.bpm.springboot</groupId>
            <artifactId>cibseven-bpm-spring-boot-starter-webapp</artifactId>
            <version>${cibseven-bpm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.cibseven.spin</groupId>
            <artifactId>cibseven-spin-dataformat-all</artifactId>
            <version>${cibseven-bpm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.cibseven.bpm.model</groupId>
            <artifactId>cibseven-bpmn-model</artifactId>
            <version>${cibseven-bpm.version}</version>
        </dependency>

        <!-- Scripting -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-jsr223</artifactId>
            <version>4.0.23</version>
        </dependency>

        <!-- Messaging -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- GEDSYS2 -->
        <dependency>
            <groupId>co.com.gedsys</groupId>
            <artifactId>gedsys2-commons</artifactId>
            <version>0.5.0-SNAPSHOT</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
            <scope>provided</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <release>${java.version}</release>
                    <annotationProcessorPaths>

                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>

                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- Surefire plugin for unit tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- Skip integration tests when running unit tests -->
                    <excludes>
                        <exclude>**/*IntegrationTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- Failsafe plugin for integration tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration>
                    <!-- Only run integration tests -->
                    <includes>
                        <include>**/*IntegrationTest.java</include>
                    </includes>
                    <skipITs>${skipITs}</skipITs>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven_central</id>
            <name>Maven Central</name>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>

        <repository>
            <id>gedsys2-commons</id>
            <name>GitHub Sucomunicacion Apache Maven Packages</name>
            <url>https://maven.pkg.github.com/Sucomunicacion/gedsys2-commons</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>mvn-cibseven-public</id>
            <name>CIB seven Public Repository</name>
            <url>https://artifacts.cibseven.org/repository/public/</url>
        </repository>
    </repositories>

</project>
