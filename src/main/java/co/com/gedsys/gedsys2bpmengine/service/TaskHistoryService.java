package co.com.gedsys.gedsys2bpmengine.service;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskHistoryDTO;
import co.com.gedsys.gedsys2bpmengine.controller.exception.TaskNotFoundException;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.UnauthorizedException;
import org.cibseven.bpm.engine.HistoryService;
import org.cibseven.bpm.engine.RepositoryService;
import org.cibseven.bpm.engine.history.HistoricTaskInstance;
import org.cibseven.bpm.engine.repository.ProcessDefinition;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TaskHistoryService {

    private final HistoryService historyService;
    private final RepositoryService repositoryService;

    public TaskHistoryService(HistoryService historyService, RepositoryService repositoryService) {
        this.historyService = historyService;
        this.repositoryService = repositoryService;
    }

    public List<TaskHistoryDTO> getTasksHistory(String username) {
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(username)
                .finished()
                .list();

        if (historicTasks.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, String> processDefinitionNames = getProcessDefinitionNames(historicTasks);

        return historicTasks.stream()
                .map(task -> createTaskHistoryDTO(task, processDefinitionNames))
                .collect(Collectors.toList());
    }

    public TaskHistoryDTO getTaskHistory(String username, String taskId) {
        HistoricTaskInstance historicTask = historyService.createHistoricTaskInstanceQuery()
                .taskId(taskId)
                .finished()
                .singleResult();

        if (historicTask == null) {
            throw new TaskNotFoundException(taskId);
        }

        if (!username.equals(historicTask.getAssignee())) {
            throw new UnauthorizedException("Usuario no autorizado para acceder al historial de esta tarea");
        }

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(historicTask.getProcessDefinitionId())
                .singleResult();

        String processName = processDefinition != null ? processDefinition.getName() : "Proceso desconocido";

        return TaskHistoryDTO.builder()
                .id(historicTask.getId())
                .taskName(historicTask.getName())
                .startDate(convertToLocalDateTime(historicTask.getStartTime()))
                .endDate(convertToLocalDateTime(historicTask.getEndTime()))
                .processName(processName)
                .build();
    }

    private Map<String, String> getProcessDefinitionNames(List<HistoricTaskInstance> historicTasks) {
        List<String> processDefinitionIds = historicTasks.stream()
                .map(HistoricTaskInstance::getProcessDefinitionId)
                .distinct()
                .collect(Collectors.toList());

        List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionIdIn(processDefinitionIds.toArray(new String[0]))
                .list();

        return processDefinitions.stream()
                .collect(Collectors.toMap(
                        ProcessDefinition::getId,
                        pd -> pd.getName() != null ? pd.getName() : "Proceso desconocido"
                ));
    }

    private TaskHistoryDTO createTaskHistoryDTO(HistoricTaskInstance task, Map<String, String> processDefinitionNames) {
        String processName = processDefinitionNames.getOrDefault(task.getProcessDefinitionId(), "Proceso desconocido");

        return TaskHistoryDTO.builder()
                .id(task.getId())
                .taskName(task.getName())
                .startDate(convertToLocalDateTime(task.getStartTime()))
                .endDate(convertToLocalDateTime(task.getEndTime()))
                .processName(processName)
                .build();
    }

    private LocalDateTime convertToLocalDateTime(java.util.Date date) {
        return date != null ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    }
}
