package co.com.gedsys.gedsys2bpmengine.infrastructure.config;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

@Configuration
public class ProcessQueueConfig {

    private static final String PROCESOS_DLK = "procesos.dlk";
    private static final String PROCESOS_DLQ = "procesos.dlq";

    // Colas de Procesos
    @Bean("procesosCompletadosQueue")
    Queue procesosCompletadosQueue() {
        return QueueBuilder.durable(QueueName.PROCESOS_COMPLETADOS)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(PROCESOS_DLK)
                .build();
    }

    @Bean("procesosCompletadosParcialmenteQueue")
    Queue procesosCompletadosParcialmenteQueue() {
        return QueueBuilder.durable(QueueName.PROCESOS_COMPLETADOS_PARCIALMENTE)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(PROCESOS_DLK)
                .build();
    }

    @Bean("procesosReanudadosQueue")
    Queue procesosReanudadosQueue() {
        return QueueBuilder.durable(QueueName.PROCESOS_REANUDADOS)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(PROCESOS_DLK)
                .build();
    }

    @Bean("processesDeadLetterQueue")
    Queue processesDeadLetterQueue() {
        return QueueBuilder.durable(PROCESOS_DLQ)
                .build();
    }

    // Bindings
    @Bean("processesDeadLetterBinding")
    @DependsOn({ "processesDeadLetterQueue", "deadLetterExchange" })
    Binding processesDeadLetterBinding(Queue processesDeadLetterQueue, TopicExchange deadLetterExchange) {
        return BindingBuilder.bind(processesDeadLetterQueue)
                .to(deadLetterExchange)
                .with(PROCESOS_DLK);
    }

    @Bean("procesosCompletadosBinding")
    @DependsOn({ "procesosCompletadosQueue", "mainTopicExchange" })
    Binding procesosCompletadosBinding(Queue procesosCompletadosQueue, TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(procesosCompletadosQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.PROCESO_COMPLETADO);
    }

    @Bean("procesosCompletadosParcialmenteBinding")
    @DependsOn({ "procesosCompletadosParcialmenteQueue", "mainTopicExchange" })
    Binding procesosCompletadosParcialmenteBinding(Queue procesosCompletadosParcialmenteQueue, TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(procesosCompletadosParcialmenteQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.PROCESO_COMPLETADO_PARCIALMENTE);
    }


    @Bean("procesosReanudadosBinding")
    @DependsOn({ "procesosReanudadosQueue", "mainTopicExchange" })
    Binding procesosReanudadosBinding(Queue procesosReanudadosQueue, TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(procesosReanudadosQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.PROCESO_REANUDADO);
    }

}
