package co.com.gedsys.gedsys2bpmengine.delegates;

import co.com.gedsys.gedsys2bpmengine.datastructucture.WorkflowStarterMessage;

import org.cibseven.bpm.engine.delegate.DelegateExecution;
import org.cibseven.bpm.engine.delegate.ExecutionListener;
import org.cibseven.bpm.engine.delegate.Expression;
import org.springframework.amqp.core.AmqpTemplate;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Component("startWorkflow")
public class DelegateStartWorkflow implements ExecutionListener {
    private final AmqpTemplate amqpTemplate;

    private Expression orchestratorQueue;
    private Expression correlationMessage;

    public DelegateStartWorkflow(
            AmqpTemplate amqpTemplate) {
        this.amqpTemplate = amqpTemplate;
    }

    @Override
    public void notify(DelegateExecution delegateExecution) {
        try {
            Map<String, Object> localVariables = delegateExecution.getVariablesLocal();

            var orchestratorQueueValue = (String) orchestratorQueue.getValue(delegateExecution);
            var createdAt = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
            var processInstanceId = delegateExecution.getProcessInstanceId();
            var executionId = delegateExecution.getId();
            var correlationMessageValue = (String) correlationMessage.getValue(delegateExecution);
            var procedure = new WorkflowStarterMessage(processInstanceId, executionId, correlationMessageValue,
                    createdAt, localVariables);

            amqpTemplate.convertAndSend(orchestratorQueueValue, procedure);

        } catch (Exception e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error al publicar el proceso",
                    e.getMessage());
        }
    }

    public void setOrchestratorQueue(Expression orchestratorQueue) {
        this.orchestratorQueue = orchestratorQueue;
    }

    public void setCorrelationMessage(Expression correlationMessage) {
        this.correlationMessage = correlationMessage;
    }
}
