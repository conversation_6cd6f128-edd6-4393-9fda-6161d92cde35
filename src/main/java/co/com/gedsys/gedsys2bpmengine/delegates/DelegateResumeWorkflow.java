package co.com.gedsys.gedsys2bpmengine.delegates;

import org.cibseven.bpm.engine.delegate.DelegateExecution;
import org.cibseven.bpm.engine.delegate.Expression;
import org.cibseven.bpm.engine.delegate.JavaDelegate;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import co.com.gedsys.commons.constant.amqp.ExchangeName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import co.com.gedsys.gedsys2bpmengine.datastructucture.ResumeWorkflowMessage;

@Component("resumeWorkflow")
public class DelegateResumeWorkflow implements JavaDelegate {

    private final AmqpTemplate amqpTemplate;

    private Expression resumeUrl;

    public DelegateResumeWorkflow(AmqpTemplate amqpTemplate) {
        this.amqpTemplate = amqpTemplate;
    }

    @Override
    public void execute(DelegateExecution delegateExecution) {
        try {
            var resumeUrlValue = (String) resumeUrl.getValue(delegateExecution);
            var procesoReanudado = new ResumeWorkflowMessage(resumeUrlValue, delegateExecution.getVariablesLocal());
            amqpTemplate.convertAndSend(ExchangeName.MAIN_TOPIC_EXCHANGE, RoutingKeyName.PROCESO_REANUDADO,
                    procesoReanudado);

        } catch (Exception e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error al procesar el mensaje de reanudación",
                    e.getMessage());
        }
    }

    public void setResumeUrl(Expression resumeUrl) {
        this.resumeUrl = resumeUrl;
    }
}
