package co.com.gedsys.gedsys2bpmengine.controller.dto;

import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

import java.util.Date;
import java.util.Map;

@Value
@Builder
@Jacksonized
public class TaskDTO {
    String id;
    String name;
    Date createTime;
    Date dueDate;
    int priority;
    String assignee;
    String description;
    String executionId;
    String formKey;
    String processDefinitionId;
    String processInstanceId;
    String taskDefinitionKey;
    Map<String, Object> variables;
}