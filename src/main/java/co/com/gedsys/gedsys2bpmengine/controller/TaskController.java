package co.com.gedsys.gedsys2bpmengine.controller;

import co.com.gedsys.gedsys2bpmengine.controller.dto.RejectionRequest;
import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskDTO;
import co.com.gedsys.gedsys2bpmengine.controller.exception.TaskNotFoundException;
import co.com.gedsys.gedsys2bpmengine.controller.mapper.TaskMapper;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import jakarta.validation.Valid;
import org.cibseven.bpm.engine.TaskService;
import org.cibseven.bpm.engine.task.Task;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/engine/api/v1/tasks")
public class TaskController {

    private final TaskService taskService;

    public TaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @GetMapping(path = "/assigned")
    public ResponseEntity<List<TaskDTO>> getAssignedTasks() {
        String username = UserContext.getUsername();
        var tasks = taskService.createTaskQuery()
                .taskAssignee(username)
                .active()
                .initializeFormKeys()
                .list();
        return ResponseEntity.ok(tasks.stream()
                .map(TaskMapper::toDTO)
                .toList());
    }

    @GetMapping(path = "/process-instance/{processInstanceId}/assigned")
    public ResponseEntity<TaskDTO> getTasksByProcessInstanceId(@PathVariable String processInstanceId) {
        String username = UserContext.getUsername();
        var tasks = taskService.createTaskQuery()
                .taskAssignee(username)
                .processInstanceId(processInstanceId)
                .active()
                .initializeFormKeys()
                .singleResult();
        return ResponseEntity.ok(TaskMapper.toDTO(tasks));
    }

    @GetMapping(path = "/{taskId}")
    public ResponseEntity<TaskDTO> getTaskById(@PathVariable String taskId) {
        var username = UserContext.getUsername();
        var task = taskService.createTaskQuery()
                .taskId(taskId)
                .taskAssignee(username)
                .initializeFormKeys()
                .singleResult();
        if (task == null) {
            throw new TaskNotFoundException(taskId);
        }
        Map<String, Object> variables = taskService.getVariables(taskId);
        TaskDTO dto = TaskMapper.toDTOWithVariables(task, variables);
        return ResponseEntity.ok(dto);
    }

    @PostMapping("/{taskId}/submit")
    @Transactional
    public ResponseEntity<Map<String, Object>> submitTask(
            @PathVariable String taskId,
            @RequestBody LinkedHashMap<String, Object> request) {
        Task task = getActiveTaskForCurrentUser(taskId);

        var formSubmissionVariableName = (String) taskService.getVariable(taskId, "_submissionName");
        if (formSubmissionVariableName == null) {
            formSubmissionVariableName = "_formSubmission";
        }

        taskService.setVariables(taskId, Map.of(formSubmissionVariableName, request));
        taskService.complete(taskId);

        return handleTaskCompletion(task, UserContext.getUsername());
    }

    @PostMapping(path = "/{taskId}/complete", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> completeTaskWithoutVariables(@PathVariable String taskId) {
        Task task = getActiveTaskForCurrentUser(taskId);
        taskService.complete(taskId);
        return handleTaskCompletion(task, UserContext.getUsername());
    }

    @PostMapping("/{id}/variables")
    public ResponseEntity<Map<String, Object>> updateTaskVariables(
            @PathVariable String id,
            @RequestBody Map<String, Object> variables) {
        var task = taskService.createTaskQuery().taskId(id).singleResult();
        if (Optional.ofNullable(task).isEmpty())
            return ResponseEntity.notFound().build();
        taskService.setVariables(id, variables);
        taskService.saveTask(task);

        return ResponseEntity.ok().body(taskService.getVariables(id));
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/{taskId}/accept")
    public ResponseEntity<Void> acceptTask(@PathVariable String taskId) {
        // String username = UserContext.getUsername();

        // Task task = Optional.ofNullable(taskService.createTaskQuery()
        //         .taskId(taskId)
        //         .taskAssignee(username)
        //         .active()
        //         .singleResult())
        //         .orElseThrow(() -> new TaskNotFoundException(taskId));

        // String formBase = task.getTaskDefinitionKey().split("_")[0];
        // if (formBase.isEmpty()) {
        //     throw new IllegalStateException(
        //             "El taskDefinitionKey de la tarea no tiene el formato esperado: " + task.getTaskDefinitionKey());
        // }

        // var documentId = (String) taskService.getVariable(taskId, VARIABLE_DOCUMENTO_PRODUCIDO);
        // if (documentId == null) {
        //     throw new IllegalStateException("El ID del documento no puede ser nulo");
        // }

        // var approvedVariableName = formBase + "Aprobado";
        // List<String> approvals = new ArrayList<>();
        // Object approvalsObj = taskService.getVariable(taskId, approvedVariableName);
        // if (approvalsObj instanceof List<?>) {
        //     approvals = ((List<?>) approvalsObj).stream()
        //             .filter(String.class::isInstance)
        //             .map(String.class::cast)
        //             .toList();
        // }

        // if (approvals.contains(username)) {
        //     throw new IllegalStateException("El usuario ya ha aprobado esta tarea");
        // }
        // approvals.add(username);
        // taskService.setVariable(taskId, approvedVariableName, approvals);
        // taskService.complete(taskId);
        // procesarAceptacionDocumento(task.getTaskDefinitionKey(), documentId, username);

        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{taskId}/reject")
    public ResponseEntity<Void> rejectTask(@PathVariable String taskId,
            @RequestBody @Valid RejectionRequest request) {
        // String username = UserContext.getUsername();

        // Task task = Optional.ofNullable(taskService.createTaskQuery()
        //         .taskId(taskId)
        //         .taskAssignee(username)
        //         .taskDefinitionKeyIn(TASK_FIRMAR_DOCUMENTO, TASK_APROBAR_DOCUMENTO)
        //         .active()
        //         .singleResult())
        //         .orElseThrow(() -> new TaskNotFoundException(taskId));

        // String formBase = task.getTaskDefinitionKey().split("_")[0];
        // if (formBase.isEmpty()) {
        //     throw new IllegalStateException(
        //             "El taskDefinitionKey de la tarea no tiene el formato esperado: " + task.getTaskDefinitionKey());
        // }

        // Object documentId = taskService.getVariable(taskId, VARIABLE_DOCUMENTO_PRODUCIDO);
        // if (documentId == null) {
        //     throw new IllegalStateException("El ID del documento no puede ser nulo");
        // }

        // String rechazosVariableName = formBase + "Rechazos";
        // Map<String, String> rechazos = new HashMap<>();
        // Object rechazosObj = taskService.getVariable(taskId, rechazosVariableName);
        // if (rechazosObj instanceof Map<?, ?>) {
        //     ((Map<?, ?>) rechazosObj).forEach((key, value) -> {
        //         if (key instanceof String && value instanceof String) {
        //             rechazos.put((String) key, (String) value);
        //         }
        //     });
        // }

        // if (rechazos.containsKey(username)) {
        //     throw new IllegalStateException("El usuario ya ha rechazado esta tarea");
        // }

        // rechazos.put(username, request.observations());
        // taskService.setVariable(taskId, rechazosVariableName, rechazos);

        // procesarRechazoDocumento(task.getTaskDefinitionKey(), documentId, username, request);
        // taskService.complete(taskId);

        return ResponseEntity.noContent().build();
    }

    private Task getActiveTaskForCurrentUser(String taskId) {
        String username = UserContext.getUsername();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .taskAssignee(username)
                .active()
                .singleResult();

        if (task == null) {
            throw new TaskNotFoundException(taskId);
        }

        return task;
    }

    private ResponseEntity<Map<String, Object>> handleTaskCompletion(Task task, String requester) {
        boolean isConcatenable = task.getTaskDefinitionKey().contains("concatenable");
        Map<String, Object> response = new HashMap<>();

        if (isConcatenable) {
            var processInstanceId = task.getProcessInstanceId();
            response.put("processInstanceId", processInstanceId);

            var nextTask = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .taskAssignee(requester)
                    .active()
                    .initializeFormKeys()
                    .singleResult();

            if (nextTask != null) {
                response.put("nextTaskId", nextTask.getId());
                response.put("nextTaskFormKey", nextTask.getFormKey());
            }
        }

        return ResponseEntity.ok(response);
    }

    // private void procesarAceptacionDocumento(String taskDefinitionKey, Object documentId, String username) {
    //     String endpoint = Objects.equals(taskDefinitionKey, TASK_FIRMAR_DOCUMENTO)
    //             ? CoreEndpoint.FIRMAR_DOCUMENTO
    //             : CoreEndpoint.APROBAR_DOCUMENTO;

    //     coreWebClient.post()
    //             .uri(endpoint, documentId)
    //             .header(CustomHeaders.USERNAME, username)
    //             .retrieve()
    //             .toBodilessEntity()
    //             .block();
    // }

    // private void procesarRechazoDocumento(String taskDefinitionKey, Object documentId, String username,
    //         RejectionRequest request) {
    //     String endpoint = Objects.equals(taskDefinitionKey, TASK_FIRMAR_DOCUMENTO)
    //             ? CoreEndpoint.RECHAZAR_DOCUMENTO
    //             : CoreEndpoint.RECHAZAR_BORRADOR;

    //     coreWebClient.post()
    //             .uri(endpoint, documentId)
    //             .header(CustomHeaders.USERNAME, username)
    //             .bodyValue(request)
    //             .retrieve()
    //             .toBodilessEntity()
    //             .block();
    // }

}
