package co.com.gedsys.gedsys2bpmengine.controller;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskHistoryDTO;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.UnauthorizedException;
import co.com.gedsys.gedsys2bpmengine.service.TaskHistoryService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/engine/api/v1/users")
public class UserController {

    private final TaskHistoryService taskHistoryService;

    public UserController(TaskHistoryService taskHistoryService) {
        this.taskHistoryService = taskHistoryService;
    }

    @GetMapping("/{username}/tasks/history")
    public ResponseEntity<List<TaskHistoryDTO>> getTasksHistory(@PathVariable String username) {
        String currentUser = UserContext.getUsername();

        if (!currentUser.equals(username)) { //todo: validar usuario admin puede ver historial de todos
            throw new UnauthorizedException("No tiene permisos para acceder al historial de tareas de otro usuario");
        }

        List<TaskHistoryDTO> tasksHistory = taskHistoryService.getTasksHistory(username);
        return ResponseEntity.ok(tasksHistory);
    }

}
