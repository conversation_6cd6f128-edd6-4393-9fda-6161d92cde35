package co.com.gedsys.gedsys2bpmengine.controller.mapper;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.cibseven.bpm.engine.task.Task;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskDTO;

public final class TaskMapper {
    
    private TaskMapper() {
        throw new UnsupportedOperationException("Utility class");
    }
    
    public static TaskDTO toDTOWithVariables(Task task, Map<String, Object> variables) {
        if (task == null) {
            return null;
        }
        
        return TaskDTO.builder()
                .id(task.getId())
                .name(task.getName())
                .description(task.getDescription())
                .assignee(task.getAssignee())
                .createTime(task.getCreateTime())
                .dueDate(task.getDueDate())
                .processInstanceId(task.getProcessInstanceId())
                .processDefinitionId(task.getProcessDefinitionId())
                .executionId(task.getExecutionId())
                .taskDefinitionKey(task.getTaskDefinitionKey())
                .priority(task.getPriority())
                .formKey(task.getFormKey() != null ? task.getFormKey() : "")
                .variables(variables != null ? new HashMap<>(variables) : Collections.emptyMap())
                .build();
    }

    public static TaskDTO toDTO(Task task) {
        if (task == null) {
            return null;
        }
        
        return TaskDTO.builder()
                .id(task.getId())
                .name(task.getName())
                .description(task.getDescription())
                .assignee(task.getAssignee())
                .createTime(task.getCreateTime())
                .dueDate(task.getDueDate())
                .processInstanceId(task.getProcessInstanceId())
                .processDefinitionId(task.getProcessDefinitionId())
                .executionId(task.getExecutionId())
                .taskDefinitionKey(task.getTaskDefinitionKey())
                .priority(task.getPriority())
                .formKey(task.getFormKey() != null ? task.getFormKey() : "")
                .variables(Collections.emptyMap())
                .build();
    }
}
