package co.com.gedsys.gedsys2bpmengine.service;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskHistoryDTO;
import co.com.gedsys.gedsys2bpmengine.controller.exception.TaskNotFoundException;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.UnauthorizedException;
import org.cibseven.bpm.engine.HistoryService;
import org.cibseven.bpm.engine.RepositoryService;
import org.cibseven.bpm.engine.history.HistoricTaskInstance;
import org.cibseven.bpm.engine.history.HistoricTaskInstanceQuery;
import org.cibseven.bpm.engine.repository.ProcessDefinition;
import org.cibseven.bpm.engine.repository.ProcessDefinitionQuery;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskHistoryServiceTest {

    @Mock
    private HistoryService historyService;

    @Mock
    private RepositoryService repositoryService;

    @Mock
    private HistoricTaskInstanceQuery historicTaskInstanceQuery;

    @Mock
    private ProcessDefinitionQuery processDefinitionQuery;

    @Mock
    private HistoricTaskInstance historicTaskInstance;

    @Mock
    private ProcessDefinition processDefinition;

    @InjectMocks
    private TaskHistoryService taskHistoryService;

    @Test
    void getTaskHistory_Success() {
        String username = "testuser";
        String taskId = "task-123";
        String processDefinitionId = "process-def-123";
        Date startTime = new Date();
        Date endTime = new Date();

        // Mock para la cadena de llamadas del HistoryService
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskId(taskId)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.singleResult()).thenReturn(historicTaskInstance);

        // Mock para los datos de la tarea histórica
        when(historicTaskInstance.getId()).thenReturn(taskId);
        when(historicTaskInstance.getName()).thenReturn("Test Task");
        when(historicTaskInstance.getAssignee()).thenReturn(username);
        when(historicTaskInstance.getProcessDefinitionId()).thenReturn(processDefinitionId);
        when(historicTaskInstance.getStartTime()).thenReturn(startTime);
        when(historicTaskInstance.getEndTime()).thenReturn(endTime);

        // Mock para la cadena de llamadas del RepositoryService
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionId(processDefinitionId)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getName()).thenReturn("Test Process");

        TaskHistoryDTO result = taskHistoryService.getTaskHistory(username, taskId);

        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals("Test Task", result.getTaskName());
        assertEquals("Test Process", result.getProcessName());
        assertNotNull(result.getStartDate());
        assertNotNull(result.getEndDate());

        // Verificar que se llamaron los métodos correctos con los parámetros correctos
        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskId(taskId);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).singleResult();
        verify(repositoryService).createProcessDefinitionQuery();
        verify(processDefinitionQuery).processDefinitionId(processDefinitionId);
        verify(processDefinitionQuery).singleResult();
    }

    @Test
    void getTaskHistory_TaskNotFound() {
        String username = "testuser";
        String taskId = "nonexistent-task";

        // Mock para la cadena de llamadas del HistoryService que retorna null
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskId(taskId)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.singleResult()).thenReturn(null);

        assertThrows(TaskNotFoundException.class, () ->
            taskHistoryService.getTaskHistory(username, taskId));

        // Verificar que se intentó buscar la tarea pero no se accedió al RepositoryService
        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskId(taskId);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).singleResult();
        verifyNoInteractions(repositoryService);
    }

    @Test
    void getTaskHistory_UnauthorizedUser() {
        String username = "testuser";
        String taskId = "task-123";
        String assignee = "otheruser";

        // Mock para la cadena de llamadas del HistoryService
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskId(taskId)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.singleResult()).thenReturn(historicTaskInstance);
        when(historicTaskInstance.getAssignee()).thenReturn(assignee);

        assertThrows(UnauthorizedException.class, () ->
            taskHistoryService.getTaskHistory(username, taskId));

        // Verificar que se encontró la tarea pero no se accedió al RepositoryService por falta de autorización
        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskId(taskId);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).singleResult();
        verify(historicTaskInstance).getAssignee();
        verifyNoInteractions(repositoryService);
    }

    @Test
    void getTaskHistory_ProcessDefinitionNotFound() {
        String username = "testuser";
        String taskId = "task-123";
        String processDefinitionId = "process-def-123";
        Date startTime = new Date();
        Date endTime = new Date();

        // Mock para la cadena de llamadas del HistoryService
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskId(taskId)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.singleResult()).thenReturn(historicTaskInstance);

        // Mock para los datos de la tarea histórica
        when(historicTaskInstance.getId()).thenReturn(taskId);
        when(historicTaskInstance.getName()).thenReturn("Test Task");
        when(historicTaskInstance.getAssignee()).thenReturn(username);
        when(historicTaskInstance.getProcessDefinitionId()).thenReturn(processDefinitionId);
        when(historicTaskInstance.getStartTime()).thenReturn(startTime);
        when(historicTaskInstance.getEndTime()).thenReturn(endTime);

        // Mock para la cadena de llamadas del RepositoryService que retorna null
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionId(processDefinitionId)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(null);

        TaskHistoryDTO result = taskHistoryService.getTaskHistory(username, taskId);

        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals("Test Task", result.getTaskName());
        assertEquals("Proceso desconocido", result.getProcessName());
        assertNotNull(result.getStartDate());
        assertNotNull(result.getEndDate());

        // Verificar que se llamaron todos los métodos correctos
        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskId(taskId);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).singleResult();
        verify(repositoryService).createProcessDefinitionQuery();
        verify(processDefinitionQuery).processDefinitionId(processDefinitionId);
        verify(processDefinitionQuery).singleResult();
    }

    @Test
    void getTaskHistory_WithNullDates() {
        String username = "testuser";
        String taskId = "task-123";
        String processDefinitionId = "process-def-123";

        // Mock para la cadena de llamadas del HistoryService
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskId(taskId)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.singleResult()).thenReturn(historicTaskInstance);

        // Mock para los datos de la tarea histórica con fechas null
        when(historicTaskInstance.getId()).thenReturn(taskId);
        when(historicTaskInstance.getName()).thenReturn("Test Task");
        when(historicTaskInstance.getAssignee()).thenReturn(username);
        when(historicTaskInstance.getProcessDefinitionId()).thenReturn(processDefinitionId);
        when(historicTaskInstance.getStartTime()).thenReturn(null);
        when(historicTaskInstance.getEndTime()).thenReturn(null);

        // Mock para la cadena de llamadas del RepositoryService
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionId(processDefinitionId)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getName()).thenReturn("Test Process");

        TaskHistoryDTO result = taskHistoryService.getTaskHistory(username, taskId);

        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals("Test Task", result.getTaskName());
        assertEquals("Test Process", result.getProcessName());
        assertNull(result.getStartDate());
        assertNull(result.getEndDate());
    }

    @Test
    void getTasksHistory_Success() {
        String username = "testuser";

        // Mock para tareas históricas
        HistoricTaskInstance task1 = mock(HistoricTaskInstance.class);
        HistoricTaskInstance task2 = mock(HistoricTaskInstance.class);
        List<HistoricTaskInstance> historicTasks = Arrays.asList(task1, task2);

        // Mock para definiciones de proceso
        ProcessDefinition processDefinition1 = mock(ProcessDefinition.class);
        ProcessDefinition processDefinition2 = mock(ProcessDefinition.class);
        List<ProcessDefinition> processDefinitions = Arrays.asList(processDefinition1, processDefinition2);

        // Configurar mocks para la consulta de tareas históricas
        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskAssignee(username)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.list()).thenReturn(historicTasks);

        // Configurar datos de las tareas
        when(task1.getId()).thenReturn("task-1");
        when(task1.getName()).thenReturn("Task 1");
        when(task1.getProcessDefinitionId()).thenReturn("process-def-1");
        when(task1.getStartTime()).thenReturn(new Date());
        when(task1.getEndTime()).thenReturn(new Date());

        when(task2.getId()).thenReturn("task-2");
        when(task2.getName()).thenReturn("Task 2");
        when(task2.getProcessDefinitionId()).thenReturn("process-def-2");
        when(task2.getStartTime()).thenReturn(new Date());
        when(task2.getEndTime()).thenReturn(new Date());

        // Configurar mocks para la consulta de definiciones de proceso
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionIdIn(any(String[].class))).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.list()).thenReturn(processDefinitions);

        when(processDefinition1.getId()).thenReturn("process-def-1");
        when(processDefinition1.getName()).thenReturn("Process 1");
        when(processDefinition2.getId()).thenReturn("process-def-2");
        when(processDefinition2.getName()).thenReturn("Process 2");

        List<TaskHistoryDTO> result = taskHistoryService.getTasksHistory(username);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("task-1", result.get(0).getId());
        assertEquals("Task 1", result.get(0).getTaskName());
        assertEquals("Process 1", result.get(0).getProcessName());
        assertEquals("task-2", result.get(1).getId());
        assertEquals("Task 2", result.get(1).getTaskName());
        assertEquals("Process 2", result.get(1).getProcessName());

        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskAssignee(username);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).list();
        verify(repositoryService).createProcessDefinitionQuery();
        verify(processDefinitionQuery).processDefinitionIdIn(any(String[].class));
        verify(processDefinitionQuery).list();
    }

    @Test
    void getTasksHistory_EmptyList() {
        String username = "testuser";

        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskAssignee(username)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.list()).thenReturn(Collections.emptyList());

        List<TaskHistoryDTO> result = taskHistoryService.getTasksHistory(username);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(historyService).createHistoricTaskInstanceQuery();
        verify(historicTaskInstanceQuery).taskAssignee(username);
        verify(historicTaskInstanceQuery).finished();
        verify(historicTaskInstanceQuery).list();
        verifyNoInteractions(repositoryService);
    }

    @Test
    void getTasksHistory_ProcessDefinitionNotFound() {
        String username = "testuser";

        HistoricTaskInstance task = mock(HistoricTaskInstance.class);
        List<HistoricTaskInstance> historicTasks = Arrays.asList(task);

        when(historyService.createHistoricTaskInstanceQuery()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.taskAssignee(username)).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.finished()).thenReturn(historicTaskInstanceQuery);
        when(historicTaskInstanceQuery.list()).thenReturn(historicTasks);

        when(task.getId()).thenReturn("task-1");
        when(task.getName()).thenReturn("Task 1");
        when(task.getProcessDefinitionId()).thenReturn("process-def-1");
        when(task.getStartTime()).thenReturn(new Date());
        when(task.getEndTime()).thenReturn(new Date());

        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionIdIn(any(String[].class))).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.list()).thenReturn(Collections.emptyList());

        List<TaskHistoryDTO> result = taskHistoryService.getTasksHistory(username);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task-1", result.get(0).getId());
        assertEquals("Task 1", result.get(0).getTaskName());
        assertEquals("Proceso desconocido", result.get(0).getProcessName());
    }
}
