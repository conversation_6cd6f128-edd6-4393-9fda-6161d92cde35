package co.com.gedsys.gedsys2bpmengine.controller.mapper;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskDTO;
import org.cibseven.bpm.engine.task.Task;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("TaskMapper Integration Tests")
class TaskMapperIntegrationTest {

    @Mock
    private Task mockTask;

    private final Date testDate = new Date();
    private final String TEST_ID = "integration-task-123";
    private final String TEST_NAME = "Integration Test Task";
    private final String TEST_ASSIGNEE = "integrationUser";

    private void setupMockTask() {
        when(mockTask.getId()).thenReturn(TEST_ID);
        when(mockTask.getName()).thenReturn(TEST_NAME);
        when(mockTask.getDescription()).thenReturn("Integration test description");
        when(mockTask.getAssignee()).thenReturn(TEST_ASSIGNEE);
        when(mockTask.getCreateTime()).thenReturn(testDate);
        when(mockTask.getDueDate()).thenReturn(new Date(testDate.getTime() + 86400000));
        when(mockTask.getProcessInstanceId()).thenReturn("process-integration-456");
        when(mockTask.getProcessDefinitionId()).thenReturn("process-def-integration-789");
        when(mockTask.getExecutionId()).thenReturn("execution-integration-101");
        when(mockTask.getTaskDefinitionKey()).thenReturn("integrationTask_1");
        when(mockTask.getPriority()).thenReturn(75);
    }

    @Test
    @DisplayName("TaskMapper debe integrar correctamente con flujo de mapeo completo")
    void taskMapper_ShouldIntegrateWithCompleteMapping() {
        // Given
        setupMockTask();
        Map<String, Object> complexVariables = new HashMap<>();
        complexVariables.put("processVar", "processValue");
        complexVariables.put("taskVar", 42);
        complexVariables.put("boolVar", true);

        // When - Simular el flujo completo de mapeo
        TaskDTO dtoWithoutVariables = TaskMapper.toDTO(mockTask);
        TaskDTO dtoWithVariables = TaskMapper.toDTOWithVariables(mockTask, complexVariables);

        // Then - Verificar que ambos métodos funcionan correctamente
        assertThat(dtoWithoutVariables).isNotNull();
        assertThat(dtoWithoutVariables.getId()).isEqualTo(TEST_ID);
        assertThat(dtoWithoutVariables.getName()).isEqualTo(TEST_NAME);
        assertThat(dtoWithoutVariables.getAssignee()).isEqualTo(TEST_ASSIGNEE);
        assertThat(dtoWithoutVariables.getVariables()).isEmpty();

        assertThat(dtoWithVariables).isNotNull();
        assertThat(dtoWithVariables.getId()).isEqualTo(TEST_ID);
        assertThat(dtoWithVariables.getName()).isEqualTo(TEST_NAME);
        assertThat(dtoWithVariables.getAssignee()).isEqualTo(TEST_ASSIGNEE);
        assertThat(dtoWithVariables.getVariables()).hasSize(3);
        assertThat(dtoWithVariables.getVariables()).containsEntry("processVar", "processValue");
        assertThat(dtoWithVariables.getVariables()).containsEntry("taskVar", 42);
        assertThat(dtoWithVariables.getVariables()).containsEntry("boolVar", true);
    }

    @Test
    @DisplayName("TaskMapper debe manejar escenarios de mapeo en cadena")
    void taskMapper_ShouldHandleChainedMappingScenarios() {
        // Given
        setupMockTask();
        Map<String, Object> originalVariables = new HashMap<>();
        originalVariables.put("step1", "value1");
        originalVariables.put("step2", 100);

        // When - Simular mapeo en cadena como en un flujo real
        TaskDTO firstMapping = TaskMapper.toDTOWithVariables(mockTask, originalVariables);

        // Simular modificación de variables (como en un proceso real)
        Map<String, Object> modifiedVariables = new HashMap<>(firstMapping.getVariables());
        modifiedVariables.put("step3", "value3");
        modifiedVariables.put("step2", 200); // Modificar valor existente

        TaskDTO secondMapping = TaskMapper.toDTOWithVariables(mockTask, modifiedVariables);

        // Then
        assertThat(firstMapping.getVariables()).hasSize(2);
        assertThat(firstMapping.getVariables().get("step2")).isEqualTo(100);

        assertThat(secondMapping.getVariables()).hasSize(3);
        assertThat(secondMapping.getVariables().get("step2")).isEqualTo(200);
        assertThat(secondMapping.getVariables()).containsEntry("step3", "value3");

        // Verificar que los campos base son iguales
        assertThat(firstMapping.getId()).isEqualTo(secondMapping.getId());
        assertThat(firstMapping.getName()).isEqualTo(secondMapping.getName());
    }

    @Test
    @DisplayName("TaskMapper debe simular comportamiento de controlador real")
    void taskMapper_ShouldSimulateRealControllerBehavior() {
        // Given - Simular el comportamiento de TaskController.getAssignedTasks()
        setupMockTask();

        // When - Simular mapeo como lo haría el controlador
        TaskDTO result = TaskMapper.toDTO(mockTask);

        // Then - Verificar que el resultado es como lo esperaría el controlador
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getName()).isEqualTo(TEST_NAME);
        assertThat(result.getAssignee()).isEqualTo(TEST_ASSIGNEE);
        assertThat(result.getVariables()).isEmpty(); // Como en getAssignedTasks()
    }

    @Test
    @DisplayName("TaskMapper debe simular comportamiento de getTaskById")
    void taskMapper_ShouldSimulateGetTaskByIdBehavior() {
        // Given - Simular el comportamiento de TaskController.getTaskById()
        setupMockTask();
        Map<String, Object> taskVariables = new HashMap<>();
        taskVariables.put("formData", "userInput");
        taskVariables.put("processStep", 3);

        // When - Simular mapeo como lo haría getTaskById
        TaskDTO result = TaskMapper.toDTOWithVariables(mockTask, taskVariables);

        // Then - Verificar que el resultado incluye variables
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getName()).isEqualTo(TEST_NAME);
        assertThat(result.getAssignee()).isEqualTo(TEST_ASSIGNEE);
        assertThat(result.getVariables()).hasSize(2);
        assertThat(result.getVariables()).containsEntry("formData", "userInput");
        assertThat(result.getVariables()).containsEntry("processStep", 3);
    }

    @Test
    @DisplayName("TaskMapper debe manejar correctamente tareas null en contexto de integración")
    void taskMapper_ShouldHandleNullTasksInIntegrationContext() {
        // When & Then - Verificar comportamiento con null como en casos reales
        assertThat(TaskMapper.toDTO(null)).isNull();
        assertThat(TaskMapper.toDTOWithVariables(null, Map.of("key", "value"))).isNull();
        assertThat(TaskMapper.toDTOWithVariables(null, null)).isNull();
    }

    @Test
    @DisplayName("TaskMapper debe ser consistente entre múltiples llamadas")
    void taskMapper_ShouldBeConsistentAcrossMultipleCalls() {
        // Given
        setupMockTask();
        Map<String, Object> variables = Map.of("test", "consistency");

        // When - Múltiples llamadas como en un entorno real
        TaskDTO result1 = TaskMapper.toDTOWithVariables(mockTask, variables);
        TaskDTO result2 = TaskMapper.toDTOWithVariables(mockTask, variables);
        TaskDTO result3 = TaskMapper.toDTO(mockTask);
        TaskDTO result4 = TaskMapper.toDTO(mockTask);

        // Then - Verificar consistencia
        assertThat(result1.getId()).isEqualTo(result2.getId());
        assertThat(result1.getName()).isEqualTo(result2.getName());
        assertThat(result1.getVariables()).isEqualTo(result2.getVariables());

        assertThat(result3.getId()).isEqualTo(result4.getId());
        assertThat(result3.getName()).isEqualTo(result4.getName());
        assertThat(result3.getVariables()).isEqualTo(result4.getVariables());
    }
}
