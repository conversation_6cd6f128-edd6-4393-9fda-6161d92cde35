package co.com.gedsys.gedsys2bpmengine.controller;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskHistoryDTO;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.UnauthorizedException;
import co.com.gedsys.gedsys2bpmengine.service.TaskHistoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserControllerTest {

    @Mock
    private TaskHistoryService taskHistoryService;

    @InjectMocks
    private UserController userController;

    private List<TaskHistoryDTO> mockTasksHistory;

    @BeforeEach
    void setUp() {
        TaskHistoryDTO task1 = TaskHistoryDTO.builder()
                .id("task-123")
                .taskName("Test Task 1")
                .startDate(LocalDateTime.of(2024, 1, 1, 10, 0))
                .endDate(LocalDateTime.of(2024, 1, 1, 11, 0))
                .processName("Test Process 1")
                .build();

        TaskHistoryDTO task2 = TaskHistoryDTO.builder()
                .id("task-456")
                .taskName("Test Task 2")
                .startDate(LocalDateTime.of(2024, 1, 2, 14, 0))
                .endDate(LocalDateTime.of(2024, 1, 2, 15, 30))
                .processName("Test Process 2")
                .build();

        mockTasksHistory = Arrays.asList(task1, task2);
    }

    @Test
    void getTasksHistory_Success() {
        String username = "testuser";

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUsername).thenReturn(username);
            when(taskHistoryService.getTasksHistory(username)).thenReturn(mockTasksHistory);

            ResponseEntity<List<TaskHistoryDTO>> response = userController.getTasksHistory(username);

            assertEquals(HttpStatus.OK, response.getStatusCode());
            List<TaskHistoryDTO> responseBody = response.getBody();
            assertNotNull(responseBody);
            assertEquals(mockTasksHistory, responseBody);
            assertEquals(2, responseBody.size());
            verify(taskHistoryService).getTasksHistory(username);
        }
    }

    @Test
    void getTasksHistory_EmptyList() {
        String username = "testuser";

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUsername).thenReturn(username);
            when(taskHistoryService.getTasksHistory(username)).thenReturn(Collections.emptyList());

            ResponseEntity<List<TaskHistoryDTO>> response = userController.getTasksHistory(username);

            assertEquals(HttpStatus.OK, response.getStatusCode());
            List<TaskHistoryDTO> responseBody = response.getBody();
            assertNotNull(responseBody);
            assertEquals(Collections.emptyList(), responseBody);
            assertTrue(responseBody.isEmpty());
            verify(taskHistoryService).getTasksHistory(username);
        }
    }

    @Test
    void getTasksHistory_UnauthorizedUser() {
        String requestedUsername = "otheruser";
        String currentUsername = "testuser";

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUsername).thenReturn(currentUsername);

            assertThrows(UnauthorizedException.class, () ->
                userController.getTasksHistory(requestedUsername));

            verify(taskHistoryService, never()).getTasksHistory(any());
        }
    }
}
