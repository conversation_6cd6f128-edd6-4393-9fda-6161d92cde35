package co.com.gedsys.gedsys2bpmengine.controller.mapper;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskDTO;
import org.cibseven.bpm.engine.task.Task;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("TaskMapper Tests")
class TaskMapperTest {

    @Mock
    private Task task;

    private final Date testDate = new Date();
    private final String TEST_ID = "task-123";
    private final String TEST_NAME = "Test Task";
    private final String TEST_DESCRIPTION = "Test Description";
    private final String TEST_ASSIGNEE = "testuser";
    private final Date TEST_DUE_DATE = new Date(testDate.getTime() + 86400000); // +1 day
    private final String TEST_PROCESS_INSTANCE_ID = "process-456";
    private final String TEST_PROCESS_DEFINITION_ID = "process-def-789";
    private final String TEST_EXECUTION_ID = "execution-101";
    private final String TEST_TASK_DEFINITION_KEY = "userTask_1";
    private final int TEST_PRIORITY = 50;

    private void setupMockTask() {
        when(task.getId()).thenReturn(TEST_ID);
        when(task.getName()).thenReturn(TEST_NAME);
        when(task.getDescription()).thenReturn(TEST_DESCRIPTION);
        when(task.getAssignee()).thenReturn(TEST_ASSIGNEE);
        when(task.getCreateTime()).thenReturn(testDate);
        when(task.getDueDate()).thenReturn(TEST_DUE_DATE);
        when(task.getProcessInstanceId()).thenReturn(TEST_PROCESS_INSTANCE_ID);
        when(task.getProcessDefinitionId()).thenReturn(TEST_PROCESS_DEFINITION_ID);
        when(task.getExecutionId()).thenReturn(TEST_EXECUTION_ID);
        when(task.getTaskDefinitionKey()).thenReturn(TEST_TASK_DEFINITION_KEY);
        when(task.getPriority()).thenReturn(TEST_PRIORITY);
    }

    @Test
    @DisplayName("toDTO debe mapear correctamente todos los campos de Task")
    void toDTO_ShouldMapAllTaskFields() {
        // Given
        setupMockTask();

        // When
        TaskDTO result = TaskMapper.toDTO(task);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getName()).isEqualTo(TEST_NAME);
        assertThat(result.getDescription()).isEqualTo(TEST_DESCRIPTION);
        assertThat(result.getAssignee()).isEqualTo(TEST_ASSIGNEE);
        assertThat(result.getCreateTime()).isEqualTo(testDate);
        assertThat(result.getDueDate()).isEqualTo(TEST_DUE_DATE);
        assertThat(result.getProcessInstanceId()).isEqualTo(TEST_PROCESS_INSTANCE_ID);
        assertThat(result.getProcessDefinitionId()).isEqualTo(TEST_PROCESS_DEFINITION_ID);
        assertThat(result.getExecutionId()).isEqualTo(TEST_EXECUTION_ID);
        assertThat(result.getTaskDefinitionKey()).isEqualTo(TEST_TASK_DEFINITION_KEY);
        assertThat(result.getPriority()).isEqualTo(TEST_PRIORITY);
        assertThat(result.getVariables()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("toDTO debe retornar null cuando Task es null")
    void toDTO_ShouldReturnNull_WhenTaskIsNull() {
        // When
        TaskDTO result = TaskMapper.toDTO(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("toDTO debe manejar campos null en Task")
    void toDTO_ShouldHandleNullFields() {
        // Given
        when(task.getId()).thenReturn(TEST_ID);
        when(task.getName()).thenReturn(null);
        when(task.getDescription()).thenReturn(null);
        when(task.getAssignee()).thenReturn(null);
        when(task.getCreateTime()).thenReturn(testDate);
        when(task.getDueDate()).thenReturn(null);
        when(task.getProcessInstanceId()).thenReturn(TEST_PROCESS_INSTANCE_ID);
        when(task.getProcessDefinitionId()).thenReturn(TEST_PROCESS_DEFINITION_ID);
        when(task.getExecutionId()).thenReturn(TEST_EXECUTION_ID);
        when(task.getTaskDefinitionKey()).thenReturn(TEST_TASK_DEFINITION_KEY);
        when(task.getPriority()).thenReturn(TEST_PRIORITY);

        // When
        TaskDTO result = TaskMapper.toDTO(task);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getName()).isNull();
        assertThat(result.getDescription()).isNull();
        assertThat(result.getAssignee()).isNull();
        assertThat(result.getDueDate()).isNull();
        assertThat(result.getVariables()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("toDTOWithVariables debe mapear Task con variables proporcionadas")
    void toDTOWithVariables_ShouldMapTaskWithProvidedVariables() {
        // Given
        setupMockTask();
        Map<String, Object> variables = new HashMap<>();
        variables.put("var1", "value1");
        variables.put("var2", 123);
        variables.put("var3", true);

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getName()).isEqualTo(TEST_NAME);
        assertThat(result.getVariables()).isEqualTo(variables);
        assertThat(result.getVariables()).hasSize(3);
        assertThat(result.getVariables().get("var1")).isEqualTo("value1");
        assertThat(result.getVariables().get("var2")).isEqualTo(123);
        assertThat(result.getVariables().get("var3")).isEqualTo(true);
    }

    @Test
    @DisplayName("toDTOWithVariables debe retornar null cuando Task es null")
    void toDTOWithVariables_ShouldReturnNull_WhenTaskIsNull() {
        // Given
        Map<String, Object> variables = Map.of("key", "value");

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(null, variables);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("toDTOWithVariables debe usar mapa vacío cuando variables es null")
    void toDTOWithVariables_ShouldUseEmptyMap_WhenVariablesIsNull() {
        // Given
        setupMockTask();

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getVariables()).isNotNull().isEmpty();
        assertThat(result.getVariables()).isEqualTo(Collections.emptyMap());
    }

    @Test
    @DisplayName("toDTOWithVariables debe manejar mapa de variables vacío")
    void toDTOWithVariables_ShouldHandleEmptyVariablesMap() {
        // Given
        setupMockTask();
        Map<String, Object> emptyVariables = new HashMap<>();

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, emptyVariables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getVariables()).isNotNull().isEmpty();
        assertThat(result.getVariables()).isEqualTo(emptyVariables);
    }

    @Test
    @DisplayName("toDTOWithVariables debe preservar tipos de datos en variables")
    void toDTOWithVariables_ShouldPreserveVariableDataTypes() {
        // Given
        setupMockTask();
        Map<String, Object> variables = new HashMap<>();
        variables.put("stringVar", "text");
        variables.put("intVar", 42);
        variables.put("boolVar", false);
        variables.put("dateVar", testDate);
        variables.put("nullVar", null);

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, variables);

        // Then
        assertThat(result.getVariables()).hasSize(5);
        assertThat(result.getVariables().get("stringVar")).isInstanceOf(String.class).isEqualTo("text");
        assertThat(result.getVariables().get("intVar")).isInstanceOf(Integer.class).isEqualTo(42);
        assertThat(result.getVariables().get("boolVar")).isInstanceOf(Boolean.class).isEqualTo(false);
        assertThat(result.getVariables().get("dateVar")).isInstanceOf(Date.class).isEqualTo(testDate);
        assertThat(result.getVariables().get("nullVar")).isNull();
    }

    @Test
    @DisplayName("TaskDTO debe ser inmutable")
    void taskDTO_ShouldBeImmutable() {
        // Given
        setupMockTask();
        Map<String, Object> originalVariables = new HashMap<>();
        originalVariables.put("key", "value");

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, originalVariables);
        
        // Then - Intentar modificar las variables originales no debe afectar el DTO
        originalVariables.put("newKey", "newValue");
        assertThat(result.getVariables()).hasSize(1);
        assertThat(result.getVariables()).doesNotContainKey("newKey");
    }

    @Test
    @DisplayName("Ambos métodos deben producir DTOs con mismos campos base")
    void bothMethods_ShouldProduceSameBaseFields() {
        // Given
        setupMockTask();
        Map<String, Object> variables = Map.of("test", "value");

        // When
        TaskDTO dtoWithoutVariables = TaskMapper.toDTO(task);
        TaskDTO dtoWithVariables = TaskMapper.toDTOWithVariables(task, variables);

        // Then - Todos los campos excepto variables deben ser iguales
        assertThat(dtoWithoutVariables.getId()).isEqualTo(dtoWithVariables.getId());
        assertThat(dtoWithoutVariables.getName()).isEqualTo(dtoWithVariables.getName());
        assertThat(dtoWithoutVariables.getDescription()).isEqualTo(dtoWithVariables.getDescription());
        assertThat(dtoWithoutVariables.getAssignee()).isEqualTo(dtoWithVariables.getAssignee());
        assertThat(dtoWithoutVariables.getCreateTime()).isEqualTo(dtoWithVariables.getCreateTime());
        assertThat(dtoWithoutVariables.getDueDate()).isEqualTo(dtoWithVariables.getDueDate());
        assertThat(dtoWithoutVariables.getProcessInstanceId()).isEqualTo(dtoWithVariables.getProcessInstanceId());
        assertThat(dtoWithoutVariables.getProcessDefinitionId()).isEqualTo(dtoWithVariables.getProcessDefinitionId());
        assertThat(dtoWithoutVariables.getExecutionId()).isEqualTo(dtoWithVariables.getExecutionId());
        assertThat(dtoWithoutVariables.getTaskDefinitionKey()).isEqualTo(dtoWithVariables.getTaskDefinitionKey());
        assertThat(dtoWithoutVariables.getPriority()).isEqualTo(dtoWithVariables.getPriority());
        
        // Variables deben ser diferentes
        assertThat(dtoWithoutVariables.getVariables()).isEmpty();
        assertThat(dtoWithVariables.getVariables()).isEqualTo(variables);
    }

    @Test
    @DisplayName("toDTO debe manejar prioridad cero")
    void toDTO_ShouldHandleZeroPriority() {
        // Given
        setupMockTask();
        when(task.getPriority()).thenReturn(0);

        // When
        TaskDTO result = TaskMapper.toDTO(task);

        // Then
        assertThat(result.getPriority()).isEqualTo(0);
    }

    @Test
    @DisplayName("toDTO debe manejar prioridad negativa")
    void toDTO_ShouldHandleNegativePriority() {
        // Given
        setupMockTask();
        when(task.getPriority()).thenReturn(-10);

        // When
        TaskDTO result = TaskMapper.toDTO(task);

        // Then
        assertThat(result.getPriority()).isEqualTo(-10);
    }

    @Test
    @DisplayName("toDTOWithVariables debe manejar variables con valores complejos")
    void toDTOWithVariables_ShouldHandleComplexVariableValues() {
        // Given
        setupMockTask();
        Map<String, Object> complexVariables = new HashMap<>();
        Map<String, String> nestedMap = Map.of("nested", "value");
        complexVariables.put("mapVar", nestedMap);
        complexVariables.put("listVar", java.util.List.of("item1", "item2"));
        complexVariables.put("arrayVar", new String[]{"a", "b"});

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, complexVariables);

        // Then
        assertThat(result.getVariables()).hasSize(3);
        assertThat(result.getVariables().get("mapVar")).isEqualTo(nestedMap);
        assertThat(result.getVariables().get("listVar")).isInstanceOf(java.util.List.class);
        assertThat(result.getVariables().get("arrayVar")).isInstanceOf(String[].class);
    }

    @Test
    @DisplayName("TaskMapper constructor debe lanzar UnsupportedOperationException")
    void taskMapperConstructor_ShouldThrowUnsupportedOperationException() {
        // When & Then
        try {
            var constructor = TaskMapper.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
            // Si llegamos aquí, el test debe fallar
            org.junit.jupiter.api.Assertions.fail("Se esperaba UnsupportedOperationException");
        } catch (Exception e) {
            assertThat(e.getCause()).isInstanceOf(UnsupportedOperationException.class);
            assertThat(e.getCause().getMessage()).isEqualTo("Utility class");
        }
    }

    @Test
    @DisplayName("toDTOWithVariables debe crear copia independiente de variables")
    void toDTOWithVariables_ShouldCreateIndependentCopyOfVariables() {
        // Given
        setupMockTask();
        Map<String, Object> originalVariables = new java.util.LinkedHashMap<>();
        originalVariables.put("first", "1");
        originalVariables.put("second", "2");
        originalVariables.put("third", "3");

        // When
        TaskDTO result = TaskMapper.toDTOWithVariables(task, originalVariables);

        // Then
        assertThat(result.getVariables()).hasSize(3);
        assertThat(result.getVariables()).containsAllEntriesOf(originalVariables);

        // Verificar que es una copia independiente
        originalVariables.put("fourth", "4");
        assertThat(result.getVariables()).hasSize(3);
        assertThat(result.getVariables()).doesNotContainKey("fourth");
    }

    @Test
    @DisplayName("Ambos métodos deben ser thread-safe")
    void bothMethods_ShouldBeThreadSafe() throws InterruptedException {
        // Given
        setupMockTask();
        Map<String, Object> variables = Map.of("concurrent", "test");
        int threadCount = 10;
        var results = new java.util.concurrent.ConcurrentLinkedQueue<TaskDTO>();
        var threads = new java.util.ArrayList<Thread>();

        // When - Ejecutar mapeo concurrentemente
        for (int i = 0; i < threadCount; i++) {
            Thread thread = new Thread(() -> {
                results.add(TaskMapper.toDTO(task));
                results.add(TaskMapper.toDTOWithVariables(task, variables));
            });
            threads.add(thread);
            thread.start();
        }

        // Esperar a que todos los threads terminen
        for (Thread thread : threads) {
            thread.join();
        }

        // Then
        assertThat(results).hasSize(threadCount * 2);
        // Verificar que todos los resultados son consistentes
        results.forEach(dto -> {
            assertThat(dto).isNotNull();
            assertThat(dto.getId()).isEqualTo(TEST_ID);
            assertThat(dto.getName()).isEqualTo(TEST_NAME);
        });
    }
}
